package service

import (
	"fmt"

	"love-software/internal/model"
	"love-software/internal/storage"
)

// configService 配置服务实现
type configService struct {
	storage storage.Storage
}

// NewConfigService 创建配置服务
func NewConfigService(storage storage.Storage) ConfigService {
	return &configService{
		storage: storage,
	}
}

// GetConfig 获取配置
func (s *configService) GetConfig() (*model.UserConfig, error) {
	config, err := s.storage.GetConfig()
	if err != nil {
		return nil, fmt.Errorf("获取配置失败: %w", err)
	}
	
	return config, nil
}

// UpdateConfig 更新配置
func (s *configService) UpdateConfig(config *model.UserConfig) error {
	if err := s.validateConfig(config); err != nil {
		return fmt.Errorf("配置验证失败: %w", err)
	}
	
	if err := s.storage.SaveConfig(config); err != nil {
		return fmt.Errorf("保存配置失败: %w", err)
	}
	
	return nil
}

// ResetConfig 重置配置
func (s *configService) ResetConfig() error {
	defaultConfig := &model.UserConfig{
		Theme:         "pink",
		Language:      "zh-CN",
		StorageType:   "local",
		BackupEnabled: true,
		BackupInterval: 7,
		NotifyEnabled: true,
	}
	
	return s.UpdateConfig(defaultConfig)
}

// validateConfig 验证配置
func (s *configService) validateConfig(config *model.UserConfig) error {
	if config == nil {
		return fmt.Errorf("配置不能为空")
	}
	
	// 验证主题
	validThemes := []string{"pink", "blue", "green", "purple", "dark"}
	if !StringSliceContains(validThemes, config.Theme) {
		return fmt.Errorf("无效的主题: %s", config.Theme)
	}

	// 验证语言
	validLanguages := []string{"zh-CN", "en-US"}
	if !StringSliceContains(validLanguages, config.Language) {
		return fmt.Errorf("无效的语言: %s", config.Language)
	}

	// 验证存储类型
	validStorageTypes := []string{"local", "redis"}
	if !StringSliceContains(validStorageTypes, config.StorageType) {
		return fmt.Errorf("无效的存储类型: %s", config.StorageType)
	}
	
	// 验证Redis配置
	if config.StorageType == "redis" && config.RedisConfig == nil {
		return fmt.Errorf("Redis存储需要配置Redis连接信息")
	}
	
	// 验证备份间隔
	if config.BackupInterval < 1 || config.BackupInterval > 365 {
		return fmt.Errorf("备份间隔必须在1-365天之间")
	}
	
	return nil
}

// 工具函数已移至 utils.go
