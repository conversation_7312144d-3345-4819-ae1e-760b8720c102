package storage

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/go-redis/redis/v8"

	"love-software/internal/model"
)

// RedisStorage Redis存储实现
type RedisStorage struct {
	client *redis.Client
	ctx    context.Context
}

// NewRedisStorage 创建Redis存储实例
func NewRedisStorage(config *model.RedisConfig) (*RedisStorage, error) {
	client := redis.NewClient(&redis.Options{
		Addr:     fmt.Sprintf("%s:%d", config.Host, config.Port),
		Password: config.Password,
		DB:       config.Database,
	})
	
	ctx := context.Background()
	
	// 测试连接
	_, err := client.Ping(ctx).Result()
	if err != nil {
		return nil, fmt.Errorf("Redis连接失败: %w", err)
	}
	
	storage := &RedisStorage{
		client: client,
		ctx:    ctx,
	}
	
	if err := storage.Initialize(); err != nil {
		return nil, err
	}
	
	return storage, nil
}

// Initialize 初始化存储
func (s *RedisStorage) Initialize() error {
	// 初始化默认配置
	configKey := "app:config"
	exists, err := s.client.Exists(s.ctx, configKey).Result()
	if err != nil {
		return fmt.Errorf("检查配置失败: %w", err)
	}
	
	if exists == 0 {
		defaultConfig := &model.UserConfig{
			Theme:         "pink",
			Language:      "zh-CN",
			StorageType:   "redis",
			BackupEnabled: true,
			BackupInterval: 7,
			NotifyEnabled: true,
		}
		
		if err := s.SaveConfig(defaultConfig); err != nil {
			return fmt.Errorf("初始化配置失败: %w", err)
		}
	}
	
	return nil
}

// Close 关闭存储
func (s *RedisStorage) Close() error {
	return s.client.Close()
}

// SaveDiaryEntry 保存日记条目
func (s *RedisStorage) SaveDiaryEntry(entry *model.DiaryEntry) error {
	key := fmt.Sprintf("diary:%s", entry.ID)
	
	data, err := json.Marshal(entry)
	if err != nil {
		return fmt.Errorf("序列化日记失败: %w", err)
	}
	
	// 保存日记数据
	if err := s.client.Set(s.ctx, key, data, 0).Err(); err != nil {
		return fmt.Errorf("保存日记失败: %w", err)
	}
	
	// 添加到日期索引
	dateKey := fmt.Sprintf("diary:date:%s", entry.Date.Format("2006-01-02"))
	if err := s.client.SAdd(s.ctx, dateKey, entry.ID).Err(); err != nil {
		return fmt.Errorf("更新日期索引失败: %w", err)
	}
	
	// 添加到全局索引
	if err := s.client.ZAdd(s.ctx, "diary:all", &redis.Z{
		Score:  float64(entry.Date.Unix()),
		Member: entry.ID,
	}).Err(); err != nil {
		return fmt.Errorf("更新全局索引失败: %w", err)
	}
	
	return nil
}

// GetDiaryEntry 获取日记条目
func (s *RedisStorage) GetDiaryEntry(id string) (*model.DiaryEntry, error) {
	key := fmt.Sprintf("diary:%s", id)
	
	data, err := s.client.Get(s.ctx, key).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, fmt.Errorf("日记不存在: %s", id)
		}
		return nil, fmt.Errorf("获取日记失败: %w", err)
	}
	
	var entry model.DiaryEntry
	if err := json.Unmarshal([]byte(data), &entry); err != nil {
		return nil, fmt.Errorf("解析日记数据失败: %w", err)
	}
	
	return &entry, nil
}

// GetDiaryEntries 获取日记条目列表
func (s *RedisStorage) GetDiaryEntries(filter *model.DiaryFilter) ([]*model.DiaryEntry, error) {
	var entryIDs []string
	
	if filter != nil && (filter.StartDate != nil || filter.EndDate != nil) {
		// 使用时间范围查询
		min := "-inf"
		max := "+inf"
		
		if filter.StartDate != nil {
			min = fmt.Sprintf("%d", filter.StartDate.Unix())
		}
		if filter.EndDate != nil {
			max = fmt.Sprintf("%d", filter.EndDate.Unix())
		}
		
		ids, err := s.client.ZRangeByScore(s.ctx, "diary:all", &redis.ZRangeBy{
			Min: min,
			Max: max,
		}).Result()
		if err != nil {
			return nil, fmt.Errorf("查询日记索引失败: %w", err)
		}
		
		entryIDs = ids
	} else {
		// 获取所有日记ID
		ids, err := s.client.ZRevRange(s.ctx, "diary:all", 0, -1).Result()
		if err != nil {
			return nil, fmt.Errorf("获取日记索引失败: %w", err)
		}
		
		entryIDs = ids
	}
	
	// 批量获取日记数据
	var entries []*model.DiaryEntry
	for _, id := range entryIDs {
		entry, err := s.GetDiaryEntry(id)
		if err != nil {
			continue // 跳过错误的条目
		}
		
		// 应用其他筛选条件
		if s.matchesFilter(entry, filter) {
			entries = append(entries, entry)
		}
	}
	
	return entries, nil
}

// matchesFilter 检查日记是否匹配筛选条件
func (s *RedisStorage) matchesFilter(entry *model.DiaryEntry, filter *model.DiaryFilter) bool {
	if filter == nil {
		return true
	}
	
	// 心情筛选
	if filter.Mood != nil && entry.Mood != *filter.Mood {
		return false
	}
	
	// 重要性筛选
	if filter.IsImportant != nil && entry.IsImportant != *filter.IsImportant {
		return false
	}
	
	// 关键词筛选
	if filter.Keyword != "" {
		found := false
		if stringContainsIgnoreCase(entry.Title, filter.Keyword) || stringContainsIgnoreCase(entry.Content, filter.Keyword) {
			found = true
		}
		for _, tag := range entry.Tags {
			if stringContainsIgnoreCase(tag, filter.Keyword) {
				found = true
				break
			}
		}
		if !found {
			return false
		}
	}
	
	// 标签筛选
	if len(filter.Tags) > 0 {
		found := false
		for _, filterTag := range filter.Tags {
			for _, entryTag := range entry.Tags {
				if entryTag == filterTag {
					found = true
					break
				}
			}
			if found {
				break
			}
		}
		if !found {
			return false
		}
	}
	
	return true
}

// UpdateDiaryEntry 更新日记条目
func (s *RedisStorage) UpdateDiaryEntry(entry *model.DiaryEntry) error {
	// 检查日记是否存在
	existing, err := s.GetDiaryEntry(entry.ID)
	if err != nil {
		return err
	}
	
	// 保留创建时间
	entry.CreatedAt = existing.CreatedAt
	entry.UpdatedAt = time.Now()
	
	return s.SaveDiaryEntry(entry)
}

// DeleteDiaryEntry 删除日记条目
func (s *RedisStorage) DeleteDiaryEntry(id string) error {
	// 获取日记信息用于清理索引
	entry, err := s.GetDiaryEntry(id)
	if err != nil {
		return err
	}
	
	// 删除日记数据
	key := fmt.Sprintf("diary:%s", id)
	if err := s.client.Del(s.ctx, key).Err(); err != nil {
		return fmt.Errorf("删除日记失败: %w", err)
	}
	
	// 清理索引
	dateKey := fmt.Sprintf("diary:date:%s", entry.Date.Format("2006-01-02"))
	s.client.SRem(s.ctx, dateKey, id)
	s.client.ZRem(s.ctx, "diary:all", id)
	
	return nil
}

// SaveConfig 保存配置
func (s *RedisStorage) SaveConfig(config *model.UserConfig) error {
	data, err := json.Marshal(config)
	if err != nil {
		return fmt.Errorf("序列化配置失败: %w", err)
	}
	
	if err := s.client.Set(s.ctx, "app:config", data, 0).Err(); err != nil {
		return fmt.Errorf("保存配置失败: %w", err)
	}
	
	return nil
}

// GetConfig 获取配置
func (s *RedisStorage) GetConfig() (*model.UserConfig, error) {
	data, err := s.client.Get(s.ctx, "app:config").Result()
	if err != nil {
		if err == redis.Nil {
			return nil, fmt.Errorf("配置不存在")
		}
		return nil, fmt.Errorf("获取配置失败: %w", err)
	}
	
	var config model.UserConfig
	if err := json.Unmarshal([]byte(data), &config); err != nil {
		return nil, fmt.Errorf("解析配置失败: %w", err)
	}
	
	return &config, nil
}

// stringContainsIgnoreCase 检查字符串是否包含子字符串（忽略大小写）
func stringContainsIgnoreCase(s, substr string) bool {
	return strings.Contains(strings.ToLower(s), strings.ToLower(substr))
}

// 其他方法的实现将在后续添加...
// 这里只实现了核心的日记和配置功能作为示例
