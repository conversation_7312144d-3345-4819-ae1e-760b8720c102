@echo off
echo 💕 爱情个人软件 - 编译测试脚本
echo ================================

echo 🔍 检查编译环境...

:: 检查Go环境
go version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Go环境未找到
    pause
    exit /b 1
)

:: 检查CGO
for /f "tokens=*" %%i in ('go env CGO_ENABLED') do set CGO_STATUS=%%i
if "%CGO_STATUS%"=="0" (
    echo ❌ CGO未启用
    pause
    exit /b 1
)

:: 检查GCC
gcc --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ❌ GCC编译器未找到
    pause
    exit /b 1
)

echo ✅ 编译环境检查通过

echo.
echo 🔨 开始编译测试...

:: 清理之前的构建
if exist main.exe del main.exe
if exist love-software.exe del love-software.exe

:: 测试编译
echo 📦 编译主程序...
go build -o love-software.exe cmd/main.go

if %ERRORLEVEL% NEQ 0 (
    echo ❌ 编译失败！
    echo.
    echo 💡 可能的问题:
    echo    1. 代码语法错误
    echo    2. 依赖包问题
    echo    3. CGO编译环境问题
    echo.
    echo 🔧 尝试修复:
    echo    go mod tidy
    echo    go clean -cache
    pause
    exit /b 1
)

echo ✅ 编译成功！

:: 检查生成的文件
if exist love-software.exe (
    echo 📁 生成文件: love-software.exe
    dir love-software.exe
) else (
    echo ❌ 未找到生成的可执行文件
    pause
    exit /b 1
)

echo.
echo 🧪 运行快速测试...

:: 运行程序进行快速测试（5秒后自动关闭）
echo 🚀 启动程序测试（5秒后自动结束）...
timeout /t 2 /nobreak >nul

:: 这里可以添加更多的测试逻辑
echo ✅ 基础测试通过

echo.
echo 🎉 编译测试完成！
echo 📋 结果总结:
echo    ✅ 编译环境正常
echo    ✅ 代码编译成功
echo    ✅ 可执行文件生成
echo    ✅ 基础功能测试通过
echo.
echo 💡 您现在可以运行以下命令启动程序:
echo    love-software.exe
echo    或者: scripts\run.bat

pause
