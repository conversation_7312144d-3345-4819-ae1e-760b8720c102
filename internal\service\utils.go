package service

import "strings"

// StringContains 检查字符串是否包含子字符串（忽略大小写）
func StringContains(s, substr string) bool {
	return strings.Contains(strings.ToLower(s), strings.ToLower(substr))
}

// StringSliceContains 检查字符串切片是否包含指定字符串
func StringSliceContains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

// StringSliceContainsIgnoreCase 检查字符串切片是否包含指定字符串（忽略大小写）
func StringSliceContainsIgnoreCase(slice []string, item string) bool {
	lowerItem := strings.ToLower(item)
	for _, s := range slice {
		if strings.ToLower(s) == lowerItem {
			return true
		}
	}
	return false
}
