# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

这是一个基于 Go + Fyne GUI 框架开发的桌面端爱情管理软件，提供恋爱日记、纪念日管理、照片收藏、情话记录等功能。

## 核心架构

### 技术栈
- **前端UI**: Fyne v2.4.5 (Go原生GUI框架)
- **后端语言**: Go 1.21+
- **数据存储**: 双存储方案 - 本地JSON/SQLite 或 Redis
- **依赖管理**: Go Modules

### 分层架构 (Clean Architecture)
```
cmd/main.go           # 应用程序入口
├── internal/ui/      # UI界面层 (Fyne组件)
├── internal/service/ # 业务逻辑层 (接口+实现)
├── internal/storage/ # 数据存储层 (本地存储/Redis)
└── internal/model/   # 数据模型层 (结构体定义)
```

### 核心服务接口
- **DiaryService**: 恋爱日记管理 (CRUD, 搜索, 筛选)
- **AnniversaryService**: 纪念日管理 (提醒, 倒计时)
- **PhotoService**: 照片收藏 (相册, 标签分类)  
- **QuoteService**: 情话收藏 (分类, 随机显示)
- **ConfigService**: 用户配置管理
- **StatisticsService**: 数据统计分析

## 常用开发命令

### 运行和构建
```bash
# 快速运行 (自动检查环境)
scripts\run.bat

# 手动运行
go run cmd/main.go

# 构建发布版本
scripts\build.bat
# 输出: dist/love-software.exe

# 功能完整性测试
scripts\test_features.bat
```

### 环境要求
- **Go 1.21+** (必需)
- **GCC编译器** (必需 - Fyne需要CGO支持)
  - 推荐: TDM-GCC 或 MSYS2
- **CGO_ENABLED=1** (自动设置)

### 开发测试
```bash
# 运行单元测试
go test ./internal/service -v

# 依赖管理
go mod tidy

# 编译检查
go build cmd/main.go
```

## 环境配置要点

### CGO 配置 (关键)
Fyne GUI框架需要CGO支持，如遇到 `build constraints exclude all Go files` 错误：
1. 运行 `scripts\fix_env.bat` 自动修复
2. 手动设置: `set CGO_ENABLED=1`
3. 确保安装GCC编译器

### 存储配置
支持两种数据存储方案，在 `config/app.json` 中配置：
- `"storage_type": "local"` - 本地JSON文件存储 (默认)
- `"storage_type": "redis"` - Redis数据库存储

## 重要文件说明

### 入口文件
- `cmd/main.go`: 应用程序启动入口，初始化UI和服务
- `config/app.json`: 应用配置文件 (窗口、主题、功能开关)

### 核心模型
- `internal/model/models.go`: 定义所有数据结构
  - DiaryEntry (日记), Anniversary (纪念日)
  - PhotoAlbum/Photo (相册/照片), LoveQuote (情话)
  - 筛选条件, 统计信息, 通知等

### 服务层接口
- `internal/service/interfaces.go`: 定义所有业务接口
- `internal/storage/interfaces.go`: 定义存储抽象接口

## 项目特性

### 数据管理
- 支持本地存储和Redis双方案
- 完整的CRUD操作和筛选功能
- 自动备份和数据恢复机制

### UI特点
- Fyne原生GUI，跨平台支持
- 响应式布局 (1200x800默认窗口)
- 多主题支持 (粉色、蓝色、绿色、紫色、暗色)

### 业务功能
- 恋爱日记: 心情评分、图片附件、重要标记
- 纪念日管理: 提前提醒、重复年度事件
- 照片收藏: 相册分类、标签管理、幻灯片
- 情话收藏: 分类管理、随机显示、收藏功能
- 统计分析: 恋爱天数、心情分布、活跃度分析