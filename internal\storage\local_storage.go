package storage

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"love-software/internal/model"
)

// LocalStorage 本地文件存储实现
type LocalStorage struct {
	dataDir string
}

// NewLocalStorage 创建本地存储实例
func NewLocalStorage(dataDir string) (*LocalStorage, error) {
	storage := &LocalStorage{
		dataDir: dataDir,
	}
	
	if err := storage.Initialize(); err != nil {
		return nil, err
	}
	
	return storage, nil
}

// Initialize 初始化存储
func (s *LocalStorage) Initialize() error {
	// 创建数据目录
	dirs := []string{
		s.dataDir,
		filepath.Join(s.dataDir, "diary"),
		filepath.Join(s.dataDir, "albums"),
		filepath.Join(s.dataDir, "photos"),
		filepath.Join(s.dataDir, "backup"),
	}
	
	for _, dir := range dirs {
		if err := os.Mkdir<PERSON>ll(dir, 0755); err != nil {
			return fmt.Errorf("创建目录失败 %s: %w", dir, err)
		}
	}
	
	// 初始化配置文件
	configPath := filepath.Join(s.dataDir, "config.json")
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		defaultConfig := &model.UserConfig{
			Theme:         "pink",
			Language:      "zh-CN",
			StorageType:   "local",
			BackupEnabled: true,
			BackupInterval: 7,
			NotifyEnabled: true,
		}
		if err := s.SaveConfig(defaultConfig); err != nil {
			return fmt.Errorf("初始化配置失败: %w", err)
		}
	}
	
	return nil
}

// Close 关闭存储
func (s *LocalStorage) Close() error {
	// 本地文件存储无需特殊关闭操作
	return nil
}

// SaveDiaryEntry 保存日记条目
func (s *LocalStorage) SaveDiaryEntry(entry *model.DiaryEntry) error {
	// 按月份组织日记文件
	monthKey := entry.Date.Format("2006-01")
	filePath := filepath.Join(s.dataDir, "diary", fmt.Sprintf("%s.json", monthKey))
	
	// 读取现有数据
	var entries []*model.DiaryEntry
	if data, err := os.ReadFile(filePath); err == nil {
		json.Unmarshal(data, &entries)
	}
	
	// 添加新条目
	entries = append(entries, entry)
	
	// 保存到文件
	data, err := json.MarshalIndent(entries, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化日记数据失败: %w", err)
	}
	
	return os.WriteFile(filePath, data, 0644)
}

// GetDiaryEntry 获取日记条目
func (s *LocalStorage) GetDiaryEntry(id string) (*model.DiaryEntry, error) {
	// 遍历所有日记文件查找
	diaryDir := filepath.Join(s.dataDir, "diary")
	files, err := os.ReadDir(diaryDir)
	if err != nil {
		return nil, fmt.Errorf("读取日记目录失败: %w", err)
	}
	
	for _, file := range files {
		if filepath.Ext(file.Name()) != ".json" {
			continue
		}
		
		filePath := filepath.Join(diaryDir, file.Name())
		data, err := os.ReadFile(filePath)
		if err != nil {
			continue
		}
		
		var entries []*model.DiaryEntry
		if err := json.Unmarshal(data, &entries); err != nil {
			continue
		}
		
		for _, entry := range entries {
			if entry.ID == id {
				return entry, nil
			}
		}
	}
	
	return nil, fmt.Errorf("日记条目不存在: %s", id)
}

// GetDiaryEntries 获取日记条目列表
func (s *LocalStorage) GetDiaryEntries(filter *model.DiaryFilter) ([]*model.DiaryEntry, error) {
	var allEntries []*model.DiaryEntry
	
	// 读取所有日记文件
	diaryDir := filepath.Join(s.dataDir, "diary")
	files, err := os.ReadDir(diaryDir)
	if err != nil {
		return nil, fmt.Errorf("读取日记目录失败: %w", err)
	}
	
	for _, file := range files {
		if filepath.Ext(file.Name()) != ".json" {
			continue
		}
		
		filePath := filepath.Join(diaryDir, file.Name())
		data, err := os.ReadFile(filePath)
		if err != nil {
			continue
		}
		
		var entries []*model.DiaryEntry
		if err := json.Unmarshal(data, &entries); err != nil {
			continue
		}
		
		allEntries = append(allEntries, entries...)
	}
	
	// 应用筛选条件
	if filter != nil {
		allEntries = s.filterDiaryEntries(allEntries, filter)
	}
	
	return allEntries, nil
}

// filterDiaryEntries 筛选日记条目
func (s *LocalStorage) filterDiaryEntries(entries []*model.DiaryEntry, filter *model.DiaryFilter) []*model.DiaryEntry {
	var filtered []*model.DiaryEntry
	
	for _, entry := range entries {
		// 日期筛选
		if filter.StartDate != nil && entry.Date.Before(*filter.StartDate) {
			continue
		}
		if filter.EndDate != nil && entry.Date.After(*filter.EndDate) {
			continue
		}
		
		// 心情筛选
		if filter.Mood != nil && entry.Mood != *filter.Mood {
			continue
		}
		
		// 重要性筛选
		if filter.IsImportant != nil && entry.IsImportant != *filter.IsImportant {
			continue
		}
		
		// 关键词筛选
		if filter.Keyword != "" {
			found := false
			if stringContains(entry.Title, filter.Keyword) || stringContains(entry.Content, filter.Keyword) {
				found = true
			}
			for _, tag := range entry.Tags {
				if stringContains(tag, filter.Keyword) {
					found = true
					break
				}
			}
			if !found {
				continue
			}
		}
		
		// 标签筛选
		if len(filter.Tags) > 0 {
			found := false
			for _, filterTag := range filter.Tags {
				for _, entryTag := range entry.Tags {
					if entryTag == filterTag {
						found = true
						break
					}
				}
				if found {
					break
				}
			}
			if !found {
				continue
			}
		}
		
		filtered = append(filtered, entry)
	}
	
	return filtered
}

// UpdateDiaryEntry 更新日记条目
func (s *LocalStorage) UpdateDiaryEntry(entry *model.DiaryEntry) error {
	// 先删除旧条目，再保存新条目
	if err := s.DeleteDiaryEntry(entry.ID); err != nil {
		return err
	}
	
	entry.UpdatedAt = time.Now()
	return s.SaveDiaryEntry(entry)
}

// DeleteDiaryEntry 删除日记条目
func (s *LocalStorage) DeleteDiaryEntry(id string) error {
	// 遍历所有日记文件查找并删除
	diaryDir := filepath.Join(s.dataDir, "diary")
	files, err := os.ReadDir(diaryDir)
	if err != nil {
		return fmt.Errorf("读取日记目录失败: %w", err)
	}
	
	for _, file := range files {
		if filepath.Ext(file.Name()) != ".json" {
			continue
		}
		
		filePath := filepath.Join(diaryDir, file.Name())
		data, err := os.ReadFile(filePath)
		if err != nil {
			continue
		}
		
		var entries []*model.DiaryEntry
		if err := json.Unmarshal(data, &entries); err != nil {
			continue
		}
		
		// 查找并删除条目
		for i, entry := range entries {
			if entry.ID == id {
				entries = append(entries[:i], entries[i+1:]...)
				
				// 保存更新后的数据
				newData, err := json.MarshalIndent(entries, "", "  ")
				if err != nil {
					return fmt.Errorf("序列化数据失败: %w", err)
				}
				
				return os.WriteFile(filePath, newData, 0644)
			}
		}
	}
	
	return fmt.Errorf("日记条目不存在: %s", id)
}

// SaveConfig 保存配置
func (s *LocalStorage) SaveConfig(config *model.UserConfig) error {
	configPath := filepath.Join(s.dataDir, "config.json")
	data, err := json.MarshalIndent(config, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化配置失败: %w", err)
	}
	
	return os.WriteFile(configPath, data, 0644)
}

// GetConfig 获取配置
func (s *LocalStorage) GetConfig() (*model.UserConfig, error) {
	configPath := filepath.Join(s.dataDir, "config.json")
	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %w", err)
	}
	
	var config model.UserConfig
	if err := json.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("解析配置失败: %w", err)
	}
	
	return &config, nil
}

// stringContains 检查字符串是否包含子字符串（忽略大小写）
func stringContains(s, substr string) bool {
	return strings.Contains(strings.ToLower(s), strings.ToLower(substr))
}

// SaveAnniversary 保存纪念日
func (s *LocalStorage) SaveAnniversary(anniversary *model.Anniversary) error {
	filePath := filepath.Join(s.dataDir, "anniversaries.json")

	// 读取现有数据
	var anniversaries []*model.Anniversary
	if data, err := os.ReadFile(filePath); err == nil {
		json.Unmarshal(data, &anniversaries)
	}

	// 添加新纪念日
	anniversaries = append(anniversaries, anniversary)

	// 保存到文件
	data, err := json.MarshalIndent(anniversaries, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化纪念日数据失败: %w", err)
	}

	return os.WriteFile(filePath, data, 0644)
}

// GetAnniversary 获取纪念日
func (s *LocalStorage) GetAnniversary(id string) (*model.Anniversary, error) {
	filePath := filepath.Join(s.dataDir, "anniversaries.json")
	data, err := os.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("读取纪念日文件失败: %w", err)
	}

	var anniversaries []*model.Anniversary
	if err := json.Unmarshal(data, &anniversaries); err != nil {
		return nil, fmt.Errorf("解析纪念日数据失败: %w", err)
	}

	for _, anniversary := range anniversaries {
		if anniversary.ID == id {
			return anniversary, nil
		}
	}

	return nil, fmt.Errorf("纪念日不存在: %s", id)
}

// GetAnniversaries 获取纪念日列表
func (s *LocalStorage) GetAnniversaries(filter *model.AnniversaryFilter) ([]*model.Anniversary, error) {
	filePath := filepath.Join(s.dataDir, "anniversaries.json")
	data, err := os.ReadFile(filePath)
	if err != nil {
		if os.IsNotExist(err) {
			return []*model.Anniversary{}, nil
		}
		return nil, fmt.Errorf("读取纪念日文件失败: %w", err)
	}

	var anniversaries []*model.Anniversary
	if err := json.Unmarshal(data, &anniversaries); err != nil {
		return nil, fmt.Errorf("解析纪念日数据失败: %w", err)
	}

	// 应用筛选条件
	if filter != nil {
		anniversaries = s.filterAnniversaries(anniversaries, filter)
	}

	return anniversaries, nil
}

// filterAnniversaries 筛选纪念日
func (s *LocalStorage) filterAnniversaries(anniversaries []*model.Anniversary, filter *model.AnniversaryFilter) []*model.Anniversary {
	var filtered []*model.Anniversary

	for _, anniversary := range anniversaries {
		// 类型筛选
		if filter.Type != "" && anniversary.Type != filter.Type {
			continue
		}

		// 日期筛选
		if filter.StartDate != nil && anniversary.Date.Before(*filter.StartDate) {
			continue
		}
		if filter.EndDate != nil && anniversary.Date.After(*filter.EndDate) {
			continue
		}

		// 重复性筛选
		if filter.IsRecurring != nil && anniversary.IsRecurring != *filter.IsRecurring {
			continue
		}

		filtered = append(filtered, anniversary)
	}

	return filtered
}

// UpdateAnniversary 更新纪念日
func (s *LocalStorage) UpdateAnniversary(anniversary *model.Anniversary) error {
	if err := s.DeleteAnniversary(anniversary.ID); err != nil {
		return err
	}
	return s.SaveAnniversary(anniversary)
}

// DeleteAnniversary 删除纪念日
func (s *LocalStorage) DeleteAnniversary(id string) error {
	filePath := filepath.Join(s.dataDir, "anniversaries.json")
	data, err := os.ReadFile(filePath)
	if err != nil {
		return fmt.Errorf("读取纪念日文件失败: %w", err)
	}

	var anniversaries []*model.Anniversary
	if err := json.Unmarshal(data, &anniversaries); err != nil {
		return fmt.Errorf("解析纪念日数据失败: %w", err)
	}

	// 查找并删除纪念日
	for i, anniversary := range anniversaries {
		if anniversary.ID == id {
			anniversaries = append(anniversaries[:i], anniversaries[i+1:]...)

			// 保存更新后的数据
			newData, err := json.MarshalIndent(anniversaries, "", "  ")
			if err != nil {
				return fmt.Errorf("序列化数据失败: %w", err)
			}

			return os.WriteFile(filePath, newData, 0644)
		}
	}

	return fmt.Errorf("纪念日不存在: %s", id)
}

// SaveLoveQuote 保存情话
func (s *LocalStorage) SaveLoveQuote(quote *model.LoveQuote) error {
	filePath := filepath.Join(s.dataDir, "quotes.json")

	// 读取现有数据
	var quotes []*model.LoveQuote
	if data, err := os.ReadFile(filePath); err == nil {
		json.Unmarshal(data, &quotes)
	}

	// 添加新情话
	quotes = append(quotes, quote)

	// 保存到文件
	data, err := json.MarshalIndent(quotes, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化情话数据失败: %w", err)
	}

	return os.WriteFile(filePath, data, 0644)
}

// GetLoveQuote 获取情话
func (s *LocalStorage) GetLoveQuote(id string) (*model.LoveQuote, error) {
	filePath := filepath.Join(s.dataDir, "quotes.json")
	data, err := os.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("读取情话文件失败: %w", err)
	}

	var quotes []*model.LoveQuote
	if err := json.Unmarshal(data, &quotes); err != nil {
		return nil, fmt.Errorf("解析情话数据失败: %w", err)
	}

	for _, quote := range quotes {
		if quote.ID == id {
			return quote, nil
		}
	}

	return nil, fmt.Errorf("情话不存在: %s", id)
}

// GetLoveQuotes 获取情话列表
func (s *LocalStorage) GetLoveQuotes(filter *model.QuoteFilter) ([]*model.LoveQuote, error) {
	filePath := filepath.Join(s.dataDir, "quotes.json")
	data, err := os.ReadFile(filePath)
	if err != nil {
		if os.IsNotExist(err) {
			return []*model.LoveQuote{}, nil
		}
		return nil, fmt.Errorf("读取情话文件失败: %w", err)
	}

	var quotes []*model.LoveQuote
	if err := json.Unmarshal(data, &quotes); err != nil {
		return nil, fmt.Errorf("解析情话数据失败: %w", err)
	}

	// 应用筛选条件
	if filter != nil {
		quotes = s.filterQuotes(quotes, filter)
	}

	return quotes, nil
}

// filterQuotes 筛选情话
func (s *LocalStorage) filterQuotes(quotes []*model.LoveQuote, filter *model.QuoteFilter) []*model.LoveQuote {
	var filtered []*model.LoveQuote

	for _, quote := range quotes {
		// 分类筛选
		if filter.Category != "" && quote.Category != filter.Category {
			continue
		}

		// 收藏筛选
		if filter.IsFavorite != nil && quote.IsFavorite != *filter.IsFavorite {
			continue
		}

		// 关键词筛选
		if filter.Keyword != "" && !stringContains(quote.Content, filter.Keyword) {
			continue
		}

		filtered = append(filtered, quote)
	}

	return filtered
}

// UpdateLoveQuote 更新情话
func (s *LocalStorage) UpdateLoveQuote(quote *model.LoveQuote) error {
	if err := s.DeleteLoveQuote(quote.ID); err != nil {
		return err
	}
	return s.SaveLoveQuote(quote)
}

// DeleteLoveQuote 删除情话
func (s *LocalStorage) DeleteLoveQuote(id string) error {
	filePath := filepath.Join(s.dataDir, "quotes.json")
	data, err := os.ReadFile(filePath)
	if err != nil {
		return fmt.Errorf("读取情话文件失败: %w", err)
	}

	var quotes []*model.LoveQuote
	if err := json.Unmarshal(data, &quotes); err != nil {
		return fmt.Errorf("解析情话数据失败: %w", err)
	}

	// 查找并删除情话
	for i, quote := range quotes {
		if quote.ID == id {
			quotes = append(quotes[:i], quotes[i+1:]...)

			// 保存更新后的数据
			newData, err := json.MarshalIndent(quotes, "", "  ")
			if err != nil {
				return fmt.Errorf("序列化数据失败: %w", err)
			}

			return os.WriteFile(filePath, newData, 0644)
		}
	}

	return fmt.Errorf("情话不存在: %s", id)
}

// SavePhotoAlbum 保存相册
func (s *LocalStorage) SavePhotoAlbum(album *model.PhotoAlbum) error {
	albumDir := filepath.Join(s.dataDir, "albums")
	filePath := filepath.Join(albumDir, fmt.Sprintf("%s.json", album.ID))

	data, err := json.MarshalIndent(album, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化相册数据失败: %w", err)
	}

	return os.WriteFile(filePath, data, 0644)
}

// GetPhotoAlbum 获取相册
func (s *LocalStorage) GetPhotoAlbum(id string) (*model.PhotoAlbum, error) {
	filePath := filepath.Join(s.dataDir, "albums", fmt.Sprintf("%s.json", id))
	data, err := os.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("读取相册文件失败: %w", err)
	}

	var album model.PhotoAlbum
	if err := json.Unmarshal(data, &album); err != nil {
		return nil, fmt.Errorf("解析相册数据失败: %w", err)
	}

	return &album, nil
}

// GetPhotoAlbums 获取相册列表
func (s *LocalStorage) GetPhotoAlbums() ([]*model.PhotoAlbum, error) {
	albumDir := filepath.Join(s.dataDir, "albums")
	files, err := os.ReadDir(albumDir)
	if err != nil {
		if os.IsNotExist(err) {
			return []*model.PhotoAlbum{}, nil
		}
		return nil, fmt.Errorf("读取相册目录失败: %w", err)
	}

	var albums []*model.PhotoAlbum
	for _, file := range files {
		if filepath.Ext(file.Name()) != ".json" {
			continue
		}

		filePath := filepath.Join(albumDir, file.Name())
		data, err := os.ReadFile(filePath)
		if err != nil {
			continue
		}

		var album model.PhotoAlbum
		if err := json.Unmarshal(data, &album); err != nil {
			continue
		}

		albums = append(albums, &album)
	}

	return albums, nil
}
